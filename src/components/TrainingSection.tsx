import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  GraduationCap, 
  Users, 
  Clock, 
  Award,
  ArrowRight,
  Monitor,
  MessageSquare,
  Target,
  Briefcase
} from 'lucide-react';

const TrainingSection = () => {
  const trainingPrograms = [
    {
      id: 'corporate-pc-skills',
      title: 'Corporate PC Skills',
      description: 'Comprehensive training programs designed to empower your team with essential computer skills for workplace success.',
      icon: Monitor,
      features: [
        'Microsoft Office Training',
        'Windows Operating System',
        'File Management & Troubleshooting',
        'System Performance Optimization'
      ],
      link: '/training/corporate-pc-skills'
    },
    {
      id: 'soft-skills-leadership',
      title: 'Leadership Development',
      description: 'Essential interpersonal and strategic skills needed to lead effectively, inspire others, and drive organizational success.',
      icon: Users,
      features: [
        'Emotional Intelligence',
        'Effective Communication',
        'Building Trust & Influence',
        'Strategic Vision & Planning'
      ],
      link: '/training/leadership-program'
    },
    {
      id: 'time-management',
      title: 'Time Management',
      description: 'Take control of your schedule, prioritize effectively, and achieve more with less stress through proven strategies.',
      icon: Clock,
      features: [
        'Goal Setting & Prioritization',
        'Planning & Scheduling',
        'Overcoming Procrastination',
        'Work-Life Balance'
      ],
      link: '/training/time-management'
    },
    {
      id: 'communication-skills',
      title: 'Communication Skills',
      description: 'Master the art of communication to foster stronger connections, reduce misunderstandings, and drive better outcomes.',
      icon: MessageSquare,
      features: [
        'Active Listening',
        'Verbal & Non-verbal Communication',
        'Conflict Resolution',
        'Public Speaking & Presentations'
      ],
      link: '/training/communication-program'
    },
    {
      id: 'oil-gas-training',
      title: 'Oil & Gas Training',
      description: 'Professional development and technical training programs for the energy sector with internationally recognized certifications.',
      icon: Award,
      features: [
        'Offshore/Onshore Safety (OPITO, IWCF)',
        'Process Operations & Maintenance',
        'Pipeline & Refinery Operations',
        'HSE (Health, Safety & Environment)'
      ],
      link: '/training/oil-gas-programs'
    },
    {
      id: 'project-management',
      title: 'Project Management',
      description: 'Develop essential project management skills to deliver successful projects on time and within budget.',
      icon: Target,
      features: [
        'Project Planning & Execution',
        'Risk Management',
        'Team Leadership',
        'Quality Assurance'
      ],
      link: '/training/project-management'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-4">
              <GraduationCap className="w-8 h-8 text-purple-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Professional <span className="text-gradient bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Training Programs</span>
            </h2>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Enhance your skills and advance your career with our comprehensive training solutions. 
            From technical skills to leadership development, we offer programs designed for professional growth.
          </p>
        </div>

        {/* Training Programs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {trainingPrograms.map((program) => {
            const IconComponent = program.icon;
            return (
              <Card 
                key={program.id} 
                className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white border-0"
              >
                <CardContent className="p-6">
                  <div className="flex items-start mb-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-600 transition-colors duration-300">
                      <IconComponent className="w-6 h-6 text-purple-600 group-hover:text-white transition-colors duration-300" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {program.title}
                      </h3>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {program.description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-2 mb-6">
                    {program.features.map((feature, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-center">
                        <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Link to={program.link}>
                    <Button 
                      variant="outline" 
                      className="w-full group-hover:bg-purple-600 group-hover:text-white group-hover:border-purple-600 transition-all duration-300"
                    >
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Invest in Your Team's Growth?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our expert trainers provide customized solutions, hands-on learning, and flexible formats 
            to meet your organization's specific needs. Contact us to discuss your training requirements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/training">
              <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8">
                View All Programs
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-50 px-8">
                <Briefcase className="mr-2 h-5 w-5" />
                Get Custom Quote
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrainingSection;
