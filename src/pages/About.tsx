
import Layout from '@/components/Layout';
import { DynamicContent } from '@/components/content/DynamicContent';
import { ContentProvider } from '@/components/content/ContentProvider';
import { DynamicValueCards } from '@/components/DynamicValueCards';
import { DynamicTimeline } from '@/components/DynamicTimeline';

const AboutContent = () => {

  return (
    <>
      {/* Dynamic About Hero Section */}
      <DynamicContent
        identifier="about-hero"
        className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20"
        fallback={
          <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="animate-fade-in">
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    About <span className="text-gradient">OfficeTech</span>
                  </h1>
                  <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                    For over 15 years, we've been at the forefront of technology innovation in Equatorial Guinea,
                    helping businesses secure their digital assets and optimize their operations with cutting-edge solutions.
                  </p>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">500+</div>
                      <div className="text-gray-600">Projects Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">98%</div>
                      <div className="text-gray-600">Client Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div className="relative animate-fade-in">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-8 text-white">
                    <h3 className="text-2xl font-bold mb-4">Why Choose Us?</h3>
                    <ul className="space-y-3">
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        15+ years of industry experience
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        Certified security professionals
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        24/7 monitoring and support
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        Local expertise, global standards
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
        }
      />

      {/* Dynamic Values Section */}
      <DynamicValueCards
        language="en"
        editable={false}
      />

      {/* Timeline Section */}
      <DynamicTimeline
        language="en"
        editable={false}
      />
    </>
  );
};

const About = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <AboutContent />
      </Layout>
    </ContentProvider>
  );
};

export default About;
