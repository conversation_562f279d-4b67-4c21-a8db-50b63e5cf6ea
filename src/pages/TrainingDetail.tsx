import { useParams, Navigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, ArrowLeft, Clock, Users, GraduationCap, Target, Monitor, Award } from 'lucide-react';
import { Link } from 'react-router-dom';

const TrainingDetailContent = () => {
  const { slug } = useParams<{ slug: string }>();

  // Training programs data (this would normally come from database)
  const programs = [
    {
      id: 'corporate-pc-skills',
      title: 'Corporate PC Skills Development',
      duration: '2-4 weeks',
      level: 'All Levels',
      participants: '15-20',
      description: 'Comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.',
      fullDescription: `<h2>Corporate PC Skills Development</h2>
      <p>At OfficeTech, we understand that in today's fast-paced business environment, having strong computer skills is essential for success. That's why we offer comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.</p>
      
      <h3>Training Programs Include:</h3>
      <ul>
        <li><strong>Microsoft Office Suite:</strong> Word, Excel, PowerPoint, Outlook, and Teams</li>
        <li><strong>Windows Operating Systems:</strong> Navigation, file management, and troubleshooting</li>
        <li><strong>Cybersecurity Awareness:</strong> Best practices for online safety and data protection</li>
        <li><strong>Network Fundamentals:</strong> Basic networking concepts and troubleshooting</li>
        <li><strong>Custom Training:</strong> Tailored programs for specific business needs</li>
      </ul>
      
      <p>Whether you're looking to enhance productivity, streamline operations, or simply stay ahead of the curve, our tailored training solutions are here to help.</p>`,
      topics: [
        'Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)',
        'Windows Operating Systems navigation and troubleshooting',
        'Cybersecurity Awareness and best practices',
        'Network Fundamentals and basic troubleshooting',
        'Custom Training tailored to business needs'
      ],
      benefits: [
        'Improved Productivity',
        'Enhanced Skills',
        'Better Security Awareness',
        'Competitive Advantage'
      ],
      deliveryOptions: [
        'On-site Training',
        'Virtual Training',
        'Hybrid Learning',
        'Self-paced Modules'
      ],
      targetAudience: [
        'Business professionals',
        'Office workers',
        'New employees',
        'Teams needing upskilling'
      ],
      icon: Monitor,
      price: 'Contact for pricing',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: 'Leadership Development Program',
      duration: '3-5 days',
      level: 'Management',
      participants: '10-15',
      description: 'Essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.',
      fullDescription: `<h2>Leadership Development Program</h2>
      <p>Our Soft Skills Training: Leadership Program is designed to empower your team with the essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.</p>
      
      <h3>Why Leadership Soft Skills Matter</h3>
      <p>Leadership is not just about making decisions or managing tasks; it's about inspiring trust, fostering collaboration, and creating a vision that motivates others to follow. Soft skills like communication, empathy, adaptability, and problem-solving are the building blocks of effective leadership.</p>
      
      <h3>Key Training Modules</h3>
      <ul>
        <li>Self-Awareness and Emotional Intelligence</li>
        <li>Effective Communication</li>
        <li>Building Trust and Influence</li>
        <li>Decision-Making and Problem-Solving</li>
        <li>Team Collaboration and Empowerment</li>
        <li>Adaptability and Change Management</li>
        <li>Visionary Leadership</li>
      </ul>`,
      topics: [
        'Self-Awareness and Emotional Intelligence',
        'Effective Communication and Active Listening',
        'Building Trust and Influence',
        'Decision-Making and Problem-Solving',
        'Team Collaboration and Empowerment',
        'Adaptability and Change Management',
        'Visionary Leadership'
      ],
      benefits: [
        'Stronger Leaders',
        'Improved Team Dynamics',
        'Higher Employee Engagement',
        'Better Decision-Making',
        'Organizational Growth'
      ],
      deliveryOptions: [
        'Workshops',
        'Virtual Training',
        'One-on-one Coaching',
        'Blended Learning'
      ],
      targetAudience: [
        'Emerging leaders',
        'Current managers',
        'Executives',
        'Teams at all levels'
      ],
      icon: Target,
      price: 'Contact for pricing',
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: 'Time Management Program',
      duration: '2-3 days',
      level: 'All Levels',
      participants: '15-25',
      description: 'Help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress.',
      fullDescription: `<h2>Time Management Program</h2>
      <p>Our Time Management Program is designed to help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress. In today's fast-paced world, mastering time management is essential for both personal and professional success.</p>

      <h3>Program Overview</h3>
      <p>This comprehensive program covers proven time management techniques, tools, and strategies that can be immediately applied to improve productivity and work-life balance.</p>

      <h3>Key Learning Areas</h3>
      <ul>
        <li>Understanding Time Management principles</li>
        <li>Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)</li>
        <li>Planning and Scheduling techniques</li>
        <li>Overcoming Procrastination</li>
        <li>Managing Distractions and focus techniques</li>
        <li>Delegation and Collaboration</li>
        <li>Stress Management and Work-Life Balance</li>
      </ul>`,
      topics: [
        'Understanding Time Management principles',
        'Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)',
        'Planning and Scheduling techniques',
        'Overcoming Procrastination',
        'Managing Distractions and focus techniques',
        'Delegation and Collaboration',
        'Stress Management and Work-Life Balance'
      ],
      benefits: [
        'Increased Productivity',
        'Better Work-Life Balance',
        'Reduced Stress',
        'Improved Focus',
        'Enhanced Goal Achievement'
      ],
      deliveryOptions: [
        'Interactive Workshops',
        'Virtual Training',
        'Self-paced Learning',
        'Group Coaching'
      ],
      targetAudience: [
        'Professionals at all levels',
        'Team leaders',
        'Entrepreneurs',
        'Anyone seeking better time management'
      ],
      icon: Clock,
      price: 'Contact for pricing',
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: 'Communication Skills Program',
      duration: '2-4 days',
      level: 'All Levels',
      participants: '12-20',
      description: 'Master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes.',
      fullDescription: `<h2>Communication Skills Program</h2>
      <p>Our Communication Skills Program is designed to help participants master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes in both personal and professional settings.</p>

      <h3>Why Communication Skills Matter</h3>
      <p>Effective communication is the foundation of successful relationships, teamwork, and leadership. Whether you're presenting to a board, collaborating with colleagues, or resolving conflicts, strong communication skills are essential.</p>

      <h3>Program Components</h3>
      <ul>
        <li>Foundations of Effective Communication</li>
        <li>Active Listening techniques</li>
        <li>Verbal and Non-Verbal Communication</li>
        <li>Written Communication skills</li>
        <li>Emotional Intelligence in Communication</li>
        <li>Conflict Resolution and Difficult Conversations</li>
        <li>Public Speaking and Presentation Skills</li>
      </ul>`,
      topics: [
        'Foundations of Effective Communication',
        'Active Listening techniques',
        'Verbal and Non-Verbal Communication',
        'Written Communication skills',
        'Emotional Intelligence in Communication',
        'Conflict Resolution and Difficult Conversations',
        'Public Speaking and Presentation Skills'
      ],
      benefits: [
        'Improved Relationships',
        'Better Team Collaboration',
        'Enhanced Leadership Skills',
        'Increased Confidence',
        'Reduced Conflicts'
      ],
      deliveryOptions: [
        'Interactive Workshops',
        'Role-playing Exercises',
        'Virtual Training',
        'One-on-one Coaching'
      ],
      targetAudience: [
        'Business professionals',
        'Team leaders',
        'Customer service representatives',
        'Anyone wanting to improve communication'
      ],
      icon: Users,
      price: 'Contact for pricing',
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: 'Professional Oil & Gas Training',
      duration: 'Varies',
      level: 'Professional',
      participants: '8-15',
      description: 'Certified, practical, and industry-relevant training to meet global energy sector standards with internationally recognized certifications.',
      fullDescription: `<h2>Professional Oil & Gas Training</h2>
      <p>Our Professional Oil & Gas Training programs are designed to provide certified, practical, and industry-relevant training that meets global energy sector standards. We offer internationally recognized certifications that enhance career prospects and ensure compliance with industry requirements.</p>

      <h3>Training Categories</h3>
      <ul>
        <li><strong>Offshore/Onshore Safety:</strong> OPITO, IWCF, IADC certified programs</li>
        <li><strong>Process Operations & Maintenance:</strong> Equipment operation and maintenance procedures</li>
        <li><strong>Pipeline & Refinery Operations:</strong> Specialized training for pipeline and refinery personnel</li>
        <li><strong>HSE (Health, Safety & Environment):</strong> Comprehensive safety and environmental training</li>
        <li><strong>Technical & Soft Skills Development:</strong> Both technical competencies and leadership skills</li>
      </ul>

      <h3>Certifications Offered</h3>
      <p>Our training programs lead to internationally recognized certifications from leading industry bodies including OPITO, IWCF, IADC, and other relevant organizations.</p>`,
      topics: [
        'Offshore/Onshore Safety (OPITO, IWCF, IADC)',
        'Process Operations & Maintenance',
        'Pipeline & Refinery Operations',
        'HSE (Health, Safety & Environment)',
        'Technical & Soft Skills Development'
      ],
      benefits: [
        'International Certifications',
        'Enhanced Career Prospects',
        'Industry Compliance',
        'Safety Excellence',
        'Technical Competency'
      ],
      deliveryOptions: [
        'On-site Training',
        'Simulation-based Learning',
        'Practical Workshops',
        'Certification Programs'
      ],
      targetAudience: [
        'Oil & Gas professionals',
        'Safety officers',
        'Operations personnel',
        'Maintenance technicians'
      ],
      icon: Award,
      price: 'Contact for pricing',
      slug: 'oil-gas-training'
    }
  ];

  const program = programs.find(p => p.slug === slug);

  if (!program) {
    return <Navigate to="/training" replace />;
  }

  const IconComponent = program.icon;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      case 'Management': return 'bg-purple-100 text-purple-800';
      case 'Professional': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      {/* Breadcrumb */}
      <section className="py-6 px-4 sm:px-6 lg:px-8 border-b bg-white">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link to="/" className="hover:text-purple-600">Home</Link>
            <span>/</span>
            <Link to="/training" className="hover:text-purple-600">Training</Link>
            <span>/</span>
            <span className="text-gray-900">{program.title}</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                  <IconComponent className="w-8 h-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                    {program.title}
                  </h1>
                  <Badge className={`mt-2 ${getLevelColor(program.level)}`}>
                    {program.level}
                  </Badge>
                </div>
              </div>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {program.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/contact">
                  <Button 
                    size="lg"
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4"
                  >
                    Enroll Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/training">
                  <Button 
                    variant="outline"
                    size="lg"
                    className="px-8 py-4"
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    Back to Programs
                  </Button>
                </Link>
              </div>
            </div>
            <div>
              <Card className="bg-white shadow-lg">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">Program Details</h3>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <Clock className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.duration}</div>
                      <div className="text-gray-500 text-sm">Duration</div>
                    </div>
                    <div className="text-center">
                      <Users className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.participants}</div>
                      <div className="text-gray-500 text-sm">Participants</div>
                    </div>
                    <div className="text-center">
                      <GraduationCap className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">Certificate</div>
                      <div className="text-gray-500 text-sm">Included</div>
                    </div>
                    <div className="text-center">
                      <Target className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.price}</div>
                      <div className="text-gray-500 text-sm">Investment</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Description */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: program.fullDescription }}
          />
        </div>
      </section>

      {/* Program Content Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Topics Covered */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Topics Covered</h3>
                <ul className="space-y-4">
                  {program.topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Program Benefits</h3>
                <ul className="space-y-4">
                  {program.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-purple-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Start Your Learning Journey?
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Contact our training experts today to discuss how this program can benefit your team and organization.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button 
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4"
              >
                Enroll Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/training">
              <Button 
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4"
              >
                View All Programs
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const TrainingDetail = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <TrainingDetailContent />
      </Layout>
    </ContentProvider>
  );
};

export default TrainingDetail;
