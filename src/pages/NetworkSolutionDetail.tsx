import { usePara<PERSON>, Navigate } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, CheckCircle, ArrowLeft, Globe, Network, Building2, Wifi, Home } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import * as Icons from "lucide-react";

const NetworkSolutionDetailContent = () => {
  const { slug } = useParams<{ slug: string }>();

  // Get all network solutions to find the one with matching slug
  const allContent = useQuery(api.content.getAllContent, {
    language: "en",
    status: "published"
  });

  // Filter for network solutions and find the one with matching slug
  const networkSolution = allContent?.find(item => 
    item.contentType?.name === "network_solution" && item.data.slug === slug
  );

  // Get Spanish version
  const spanishContent = useQuery(api.content.getContent, {
    identifier: networkSolution?.identifier || "",
    language: "es",
    includeDraft: false
  });

  const isLoading = allContent === undefined;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
        {/* Hero Section Skeleton */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <Skeleton className="h-12 w-3/4 mb-6" />
                <Skeleton className="h-6 w-full mb-4" />
                <Skeleton className="h-6 w-5/6 mb-8" />
                <Skeleton className="h-12 w-40" />
              </div>
              <div>
                <Skeleton className="w-full h-96 rounded-lg" />
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  if (!networkSolution) {
    return <Navigate to="/network-solutions" replace />;
  }

  const getIconComponent = (iconName: string) => {
    if (!iconName) return Icons.Network;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent || Icons.Network;
  };

  const IconComponent = getIconComponent(networkSolution.data.icon);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Breadcrumb */}
      <section className="py-6 px-4 sm:px-6 lg:px-8 border-b bg-white">
        <div className="max-w-7xl mx-auto">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link to="/" className="hover:text-blue-600">Home</Link>
            <span>/</span>
            <Link to="/network-solutions" className="hover:text-blue-600">Network Solutions</Link>
            <span>/</span>
            <span className="text-gray-900">{networkSolution.data.title}</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                  <IconComponent className="w-8 h-8 text-blue-600" />
                </div>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                  {networkSolution.data.title}
                </h1>
              </div>
              <div 
                className="text-xl text-gray-600 mb-8 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: networkSolution.data.description }}
              />
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/contact">
                  <Button 
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4"
                  >
                    Get Started
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/network-solutions">
                  <Button 
                    variant="outline"
                    size="lg"
                    className="px-8 py-4"
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    Back to Solutions
                  </Button>
                </Link>
              </div>
            </div>
            <div>
              {networkSolution.data.image ? (
                <img 
                  src={networkSolution.data.image} 
                  alt={networkSolution.data.title}
                  className="w-full h-96 object-cover rounded-lg shadow-lg"
                />
              ) : (
                <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg shadow-lg flex items-center justify-center">
                  <IconComponent className="w-24 h-24 text-blue-600" />
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Description */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: networkSolution.data.fullDescription }}
          />
        </div>
      </section>

      {/* Features and Benefits */}
      {(networkSolution.data.features?.length > 0 || networkSolution.data.benefits?.length > 0) && (
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {networkSolution.data.features?.length > 0 && (
                <Card className="bg-white shadow-lg">
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h3>
                    <ul className="space-y-4">
                      {networkSolution.data.features.map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {networkSolution.data.benefits?.length > 0 && (
                <Card className="bg-white shadow-lg">
                  <CardContent className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">Benefits</h3>
                    <ul className="space-y-4">
                      {networkSolution.data.benefits.map((benefit: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-6 h-6 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Technologies */}
      {networkSolution.data.technologies?.length > 0 && (
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Technologies Used</h3>
            <div className="flex flex-wrap justify-center gap-4">
              {networkSolution.data.technologies.map((tech: string, index: number) => (
                <span 
                  key={index}
                  className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-medium"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Implement {networkSolution.data.title}?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Contact our network experts today to discuss how we can implement this solution for your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button 
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
              >
                Contact Us Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/network-solutions">
              <Button 
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4"
              >
                View All Solutions
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const NetworkSolutionDetail = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <NetworkSolutionDetailContent />
      </Layout>
    </ContentProvider>
  );
};

export default NetworkSolutionDetail;
